# Consecutive Check-in Streak Monitoring System

## Overview

This document describes the implementation of a proactive daily scheduled job that monitors users for broken consecutive check-in streaks and automatically resets their progress when they miss more than 24 hours without checking in.

## Problem Addressed

**Issue**: The existing system only reset consecutive check-in streaks **reactively** when users tried to check in again after missing days. Users who stopped checking in completely never had their streaks reset, leading to:

1. **Stale Progress**: Users with broken streaks maintained their `StreakCount` and `ProgressValue` indefinitely
2. **Incorrect UI Display**: The API continued showing outdated streak information
3. **Milestone Confusion**: Users saw progress toward milestones they could no longer achieve
4. **Data Inconsistency**: Database contained outdated streak information

**Solution**: Implement a proactive daily monitoring job that automatically detects and resets broken consecutive check-in streaks.

## Implementation Details

### 1. Daily Scheduled Job

**File**: `internal/task/activity_cashback/scheduled_tasks.go`

```go
// ConsecutiveCheckinStreakMonitoring monitors and resets broken consecutive check-in streaks
func (t *ActivityCashbackScheduledTasks) ConsecutiveCheckinStreakMonitoring() {
    global.GVA_LOG.Info("Starting consecutive check-in streak monitoring job")

    ctx := context.Background()

    // Use the new streak monitoring method
    if err := t.service.MonitorAndResetBrokenConsecutiveStreaks(ctx); err != nil {
        global.GVA_LOG.Error("Failed to monitor consecutive check-in streaks", zap.Error(err))
        return
    }

    global.GVA_LOG.Info("Consecutive check-in streak monitoring completed successfully")
}
```

**Schedule**: Daily at 00:05 UTC (5 minutes after daily reset)
**Cron Expression**: `"0 5 0 * * *"`

### 2. Core Monitoring Logic

**File**: `internal/service/activity_cashback/task_management_service.go`

```go
// MonitorAndResetBrokenConsecutiveStreaks proactively monitors all users for broken consecutive check-in streaks
func (s *TaskManagementService) MonitorAndResetBrokenConsecutiveStreaks(ctx context.Context) error {
    // 1. Get all consecutive check-in tasks
    // 2. For each task, get all users with progress
    // 3. For each user, check if they have missed their consecutive check-in
    // 4. If missed, reset their streak progress while preserving lifetime achievements
    // 5. Log the reset action and report summary statistics
}
```

### 3. Streak Detection Logic

```go
// hasUserMissedConsecutiveCheckin checks if a user has missed their consecutive check-in
func (s *TaskManagementService) hasUserMissedConsecutiveCheckin(progress model.UserTaskProgress) bool {
    // A user has missed their consecutive check-in if:
    // 1. They have an active streak (StreakCount > 0)
    // 2. Their last check-in was more than 24 hours ago (not yesterday or today)
    
    if progress.StreakCount == 0 || progress.LastCompletedAt == nil {
        return false
    }

    today := time.Now().Truncate(24 * time.Hour)
    yesterday := today.Add(-24 * time.Hour)
    lastCheckIn := progress.LastCompletedAt.Truncate(24 * time.Hour)

    // User has missed if their last check-in was before yesterday
    return lastCheckIn.Before(yesterday)
}
```

### 4. Progress Reset Logic

```go
// resetUserConsecutiveCheckinProgress resets a user's consecutive check-in progress
func (s *TaskManagementService) resetUserConsecutiveCheckinProgress(ctx context.Context, progress *model.UserTaskProgress, task *model.ActivityTask) error {
    // Reset streak-related fields
    progress.StreakCount = 0
    progress.ProgressValue = 0
    progress.Status = model.TaskStatusNotStarted

    // Reset target value to first milestone
    if task.Conditions != nil && len(task.Conditions.ConsecutiveCheckinMilestones) > 0 {
        firstMilestone := findFirstMilestone(task.Conditions.ConsecutiveCheckinMilestones)
        progress.TargetValue = &firstMilestone.Days
    }

    // Update timestamps
    now := time.Now()
    progress.UpdatedAt = now
    progress.LastResetAt = &now

    // PRESERVE lifetime achievements:
    // - CompletionCount (total times user has completed milestones)
    // - PointsEarned (total points earned from all milestone completions)

    return s.progressRepo.Update(ctx, progress)
}
```

## Key Design Principles

### 1. **Proactive Monitoring**
- Runs daily at 00:05 UTC, 5 minutes after daily task reset
- Automatically detects users who have missed consecutive check-ins
- No user action required to trigger streak resets

### 2. **Preserve Lifetime Achievements**
- **RESET**: `StreakCount`, `ProgressValue`, `Status`, `TargetValue`
- **PRESERVE**: `CompletionCount`, `PointsEarned`, user identity, task association
- Users don't lose their historical milestone achievements

### 3. **24-Hour Grace Period**
- Users can check in today or yesterday without losing their streak
- Only reset streaks when last check-in was 2+ days ago
- Maintains the "consecutive" nature while being user-friendly

### 4. **Integration with Existing Systems**
- Works seamlessly with `FrequencyProgressive` classification
- Compatible with existing streak management in `updateConsecutiveCheckinTasks`
- Integrates with daily task reset system
- Preserves milestone progression system

## Workflow

### Daily Monitoring Process

1. **Task Discovery**: Find all `TaskIDConsecutiveCheckinConfigurable` tasks
2. **User Enumeration**: Get all users with progress for each consecutive check-in task
3. **Streak Validation**: For each user, check if they have an active streak (`StreakCount > 0`)
4. **Miss Detection**: Determine if user's last check-in was more than 24 hours ago
5. **Progress Reset**: Reset streak progress while preserving lifetime achievements
6. **Logging**: Log each reset action with user ID, task ID, and previous streak count
7. **Summary Report**: Report total users processed and streaks reset

### Example Scenarios

#### Scenario 1: User Maintains Streak
- **Last Check-in**: Yesterday
- **Current Streak**: 5 days
- **Action**: No reset (user is still consecutive)
- **Result**: Streak preserved

#### Scenario 2: User Breaks Streak
- **Last Check-in**: 3 days ago
- **Current Streak**: 7 days
- **Action**: Reset streak progress
- **Result**: 
  - `StreakCount`: 7 → 0
  - `ProgressValue`: 7 → 0
  - `Status`: IN_PROGRESS → NOT_STARTED
  - `TargetValue`: 30 → 1 (first milestone)
  - `CompletionCount`: Preserved
  - `PointsEarned`: Preserved

## Configuration

### Cron Schedule
```yaml
# config.yaml
cron-tasks:
  - id: "activity_cashback_streak_monitoring"
    cron: "0 5 0 * * *"  # Daily at 00:05 UTC (after daily reset)
```

### Task Registration
```go
// internal/initializer/task.go
err = scheduler.Register(
    global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackStreakMonitoring].ID, 
    global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackStreakMonitoring].Cron, 
    activityCashbackTasks.ConsecutiveCheckinStreakMonitoring
)
```

## Testing

### Unit Tests
- **`TestConsecutiveStreakMonitoring`**: Tests core monitoring logic
- **`TestStreakMonitoringScheduledTask`**: Tests scheduled task integration
- **`TestStreakMonitoringBehavior`**: Tests expected workflow and preservation logic

### Test Coverage
- ✅ Streak miss detection logic
- ✅ Progress reset behavior
- ✅ Lifetime achievement preservation
- ✅ Scheduled task configuration
- ✅ Integration with existing systems

## Monitoring and Logging

### Log Messages
```
INFO: Starting consecutive check-in streak monitoring job
INFO: Reset broken consecutive check-in streak (user_id=..., task_id=..., previous_streak=5, last_checkin=2024-01-15)
INFO: Consecutive check-in streak monitoring completed (users_processed=1250, streaks_reset=23, consecutive_tasks=1)
ERROR: Failed to monitor consecutive check-in streaks (error details)
```

### Metrics Tracked
- **Users Processed**: Total users with consecutive check-in progress
- **Streaks Reset**: Number of broken streaks detected and reset
- **Tasks Monitored**: Number of consecutive check-in tasks processed
- **Execution Time**: Duration of monitoring job

## Benefits

### 1. **Data Consistency**
- Database always reflects accurate streak information
- No stale progress data for inactive users
- Consistent UI display across all users

### 2. **User Experience**
- Clear and accurate progress indicators
- Proper milestone targeting after breaks
- No confusion about achievable milestones

### 3. **System Reliability**
- Proactive maintenance reduces reactive fixes
- Automated process requires no manual intervention
- Comprehensive logging for monitoring and debugging

### 4. **Performance**
- Runs during low-traffic hours (00:05 UTC)
- Efficient batch processing of all users
- Minimal impact on user-facing operations

## Integration Points

### Works With
- ✅ `FrequencyProgressive` task classification
- ✅ Existing `updateConsecutiveCheckinTasks` logic
- ✅ Daily task reset system (`ResetDailyTasks`)
- ✅ Milestone progression system
- ✅ Dynamic points calculation
- ✅ Task completion factory pattern

### Preserves
- ✅ All existing API behavior
- ✅ User milestone achievements
- ✅ Points earned from completed milestones
- ✅ Task completion history
- ✅ Daily job scheduling for other tasks

The consecutive check-in streak monitoring system provides comprehensive, proactive management of user streaks while maintaining data integrity and preserving user achievements.
