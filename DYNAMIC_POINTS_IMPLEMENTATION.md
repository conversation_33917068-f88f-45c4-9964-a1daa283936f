# Dynamic Points Implementation for Consecutive Check-in Tasks

## Overview

This document describes the implementation of dynamic points display for `CONSECUTIVE_CHECKIN_CONFIGURABLE` tasks in the `userTaskListByCategory` API response.

## Problem Solved

**Before**: The API returned `"points": 0` for consecutive check-in tasks because the base task has 0 points (points are configured per milestone).

**After**: The API now returns dynamic points based on the user's current milestone progress, showing the points they can earn for their next achievable milestone.

## Implementation Details

### 1. Enhanced Task Enhancement Method

**File**: `internal/service/activity_cashback/activity_cashback_service.go`

The `enhanceConsecutiveCheckinTask` method now includes dynamic points calculation:

```go
// Update points to show the next achievable milestone points
dynamicPoints := s.calculateDynamicMilestonePoints(task.Conditions.ConsecutiveCheckinMilestones, currentStreak)
enhancedTask.Points = dynamicPoints
```

### 2. Dynamic Points Calculation Logic

**Method**: `calculateDynamicMilestonePoints`

```go
func (s *ActivityCashbackService) calculateDynamicMilestonePoints(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) int {
    if len(milestones) == 0 {
        return 0
    }

    // Get the next milestone the user is working towards
    nextMilestone := GetNextMilestone(milestones, currentStreak)
    if nextMilestone != nil {
        // Show points for the next achievable milestone
        return nextMilestone.Points
    }

    // If no next milestone (all completed), show points from the highest milestone
    highestMilestone := s.getHighestMilestone(milestones)
    if highestMilestone != nil {
        return highestMilestone.Points
    }

    // Fallback to 0 if no milestones found
    return 0
}
```

### 3. Helper Method for Highest Milestone

**Method**: `getHighestMilestone`

```go
func (s *ActivityCashbackService) getHighestMilestone(milestones []model.ConsecutiveCheckinMilestone) *model.ConsecutiveCheckinMilestone {
    if len(milestones) == 0 {
        return nil
    }

    highest := &milestones[0]
    for i := 1; i < len(milestones); i++ {
        if milestones[i].Days > highest.Days {
            highest = &milestones[i]
        }
    }
    return highest
}
```

## Behavior Examples

### User's Example Configuration
```json
{
  "consecutive_checkin_milestones": [
    {"days": 1, "points": 50},
    {"days": 2, "points": 200},
    {"days": 4, "points": 200}
  ]
}
```

### Dynamic Points Display

| User Streak | Next Milestone | Points Displayed | Explanation |
|-------------|----------------|------------------|-------------|
| 0 days      | 1 day (50 pts) | **50**          | Working towards 1-day milestone |
| 1 day       | 2 days (200 pts) | **200**       | Working towards 2-day milestone |
| 2 days      | 4 days (200 pts) | **200**       | Working towards 4-day milestone |
| 3 days      | 4 days (200 pts) | **200**       | Working towards 4-day milestone |
| 4+ days     | All completed   | **200**        | Show highest milestone points |

### API Response Transformation

**Before Enhancement**:
```json
{
  "task": {
    "id": "task-uuid",
    "name": "Custom Consecutive Check-in",
    "points": 0
  },
  "progress": {
    "streakCount": 1,
    "targetValue": 2
  }
}
```

**After Enhancement**:
```json
{
  "task": {
    "id": "task-uuid", 
    "name": "Custom Consecutive Check-in (Daily 2)",
    "points": 200
  },
  "progress": {
    "streakCount": 1,
    "targetValue": 2
  }
}
```

## Integration Flow

1. **API Call**: `userTaskListByCategory` is called
2. **Task Filtering**: Consecutive check-in tasks are identified
3. **Task Enhancement**: `enhanceConsecutiveCheckinTask` is called for each consecutive check-in task
4. **Progress Retrieval**: User's current streak count is retrieved
5. **Dynamic Points Calculation**: Points are calculated based on next achievable milestone
6. **Response**: Enhanced task with dynamic points is returned

## Testing

### Test Coverage

- **Unit Tests**: `TestCalculateDynamicMilestonePoints` - Tests core logic with various configurations
- **Helper Tests**: `TestGetHighestMilestone` - Tests milestone finding logic
- **Integration Tests**: `TestDynamicPointsIntegration` - Tests complete flow
- **Scenario Tests**: `TestUserExampleScenario` - Tests exact user example

### Test Results
```
✅ All tests passing (25+ test cases)
✅ Service builds successfully
✅ GraphQL controller builds successfully
✅ No compilation errors
```

## Files Modified

1. **`internal/service/activity_cashback/activity_cashback_service.go`**
   - Enhanced `enhanceConsecutiveCheckinTask` method
   - Added `calculateDynamicMilestonePoints` method
   - Added `getHighestMilestone` helper method

2. **`internal/service/activity_cashback/dynamic_points_test.go`** (New)
   - Comprehensive unit tests for dynamic points logic

3. **`internal/service/activity_cashback/dynamic_points_integration_test.go`** (New)
   - Integration tests for complete flow

## Backward Compatibility

- ✅ **Fully backward compatible** - no breaking changes to existing APIs
- ✅ **Non-consecutive tasks unaffected** - only consecutive check-in tasks show dynamic points
- ✅ **Existing milestone logic preserved** - all existing functionality remains intact
- ✅ **Fallback handling** - graceful degradation for edge cases

## Performance Impact

- **Minimal**: Dynamic calculation only occurs during task enhancement
- **Efficient**: Uses existing milestone utility functions
- **Cached**: Task enhancement happens once per API call
- **Scalable**: O(n) complexity where n is number of milestones (typically small)

## Future Enhancements

1. **Caching**: Could cache dynamic points calculations for frequently accessed tasks
2. **Localization**: Points display could be localized based on user preferences
3. **Analytics**: Track which milestone points are most motivating to users
4. **A/B Testing**: Test different point display strategies
