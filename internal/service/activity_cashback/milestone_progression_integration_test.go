package activity_cashback

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestMilestoneProgressionIntegrationFix tests the integration between UpdateStreak and calculateMilestoneProgress
func TestMilestoneProgressionIntegrationFix(t *testing.T) {
	t.Run("UpdateStreak_Integration", func(t *testing.T) {
		service := &TaskProgressService{}

		// Create a mock task with milestones
		taskIdentifier := model.TaskIDConsecutiveCheckinConfigurable
		task := &model.ActivityTask{
			TaskIdentifier: &taskIdentifier,
			Conditions: &model.TaskConditions{
				ConsecutiveCheckinMilestones: []model.ConsecutiveCheckinMilestone{
					{Days: 1, Points: 10},
					{Days: 2, Points: 50},
					{Days: 3, Points: 100},
				},
			},
		}

		// Create initial progress
		progress := &model.UserTaskProgress{
			StreakCount:     0,
			CompletionCount: 0,
			ProgressValue:   0,
		}

		// Test the integration: when UpdateStreak calls calculateMilestoneProgress
		// it should pass the correct CompletionCount parameter

		// Simulate Day 1: streak = 1, no milestones completed yet
		progress.StreakCount = 1
		progress.CompletionCount = 0
		calculatedProgress := service.calculateMilestoneProgress(
			task.Conditions.ConsecutiveCheckinMilestones,
			progress.StreakCount,
			progress.CompletionCount,
		)
		assert.Equal(t, 1, calculatedProgress, "Day 1: Should show 1 toward first milestone")

		// Simulate Day 1 milestone completion
		progress.CompletionCount = 1
		calculatedProgress = service.calculateMilestoneProgress(
			task.Conditions.ConsecutiveCheckinMilestones,
			progress.StreakCount,
			progress.CompletionCount,
		)
		assert.Equal(t, 1, calculatedProgress, "Day 1 completed: Should show 1 toward second milestone")

		// Simulate Day 2: streak = 2, 1 milestone completed
		progress.StreakCount = 2
		progress.CompletionCount = 1
		calculatedProgress = service.calculateMilestoneProgress(
			task.Conditions.ConsecutiveCheckinMilestones,
			progress.StreakCount,
			progress.CompletionCount,
		)
		assert.Equal(t, 1, calculatedProgress, "Day 2: Should show 1 toward second milestone (2-1=1)")

		// Simulate Day 2 milestone completion
		progress.CompletionCount = 2
		calculatedProgress = service.calculateMilestoneProgress(
			task.Conditions.ConsecutiveCheckinMilestones,
			progress.StreakCount,
			progress.CompletionCount,
		)
		assert.Equal(t, 1, calculatedProgress, "Day 2 completed: Should show 1 toward third milestone")

		// Simulate Day 3: streak = 3, 2 milestones completed (THE BUG SCENARIO)
		progress.StreakCount = 3
		progress.CompletionCount = 2
		calculatedProgress = service.calculateMilestoneProgress(
			task.Conditions.ConsecutiveCheckinMilestones,
			progress.StreakCount,
			progress.CompletionCount,
		)
		assert.Equal(t, 1, calculatedProgress, "Day 3: Should show 1 toward third milestone (3-2=1), NOT 3")

		// Simulate Day 3 milestone completion
		progress.CompletionCount = 3
		calculatedProgress = service.calculateMilestoneProgress(
			task.Conditions.ConsecutiveCheckinMilestones,
			progress.StreakCount,
			progress.CompletionCount,
		)
		assert.Equal(t, 3, calculatedProgress, "Day 3 completed: Should show 3 (all milestones completed)")
	})

	t.Run("Target_Value_Calculation", func(t *testing.T) {
		service := &TaskProgressService{}

		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 10},
			{Days: 2, Points: 50},
			{Days: 3, Points: 100},
		}

		// Test target value calculation based on completion count
		// When 0 milestones completed, target should be 1
		nextMilestone := service.getNextMilestone(milestones, 0)
		assert.NotNil(t, nextMilestone)
		assert.Equal(t, 1, nextMilestone.Days, "No milestones completed: Target should be 1-day milestone")

		// When 1 milestone completed (streak could be 1), target should be 2
		nextMilestone = service.getNextMilestone(milestones, 1)
		assert.NotNil(t, nextMilestone)
		assert.Equal(t, 2, nextMilestone.Days, "1 milestone completed: Target should be 2-day milestone")

		// When 2 milestones completed (streak could be 2), target should be 3
		nextMilestone = service.getNextMilestone(milestones, 2)
		assert.NotNil(t, nextMilestone)
		assert.Equal(t, 3, nextMilestone.Days, "2 milestones completed: Target should be 3-day milestone")

		// When all milestones completed (streak >= 3), no more targets
		nextMilestone = service.getNextMilestone(milestones, 3)
		assert.Nil(t, nextMilestone, "All milestones completed: No more targets")
	})
}
