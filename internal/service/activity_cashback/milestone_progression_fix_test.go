package activity_cashback

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestMilestoneProgressionFix tests the corrected milestone progression logic
func TestMilestoneProgressionFix(t *testing.T) {
	service := &TaskProgressService{}

	// Test milestones: 1, 2, 3 days
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 1, Points: 50},
		{Days: 2, Points: 100},
		{Days: 3, Points: 200},
	}

	t.Run("Milestone_Progression_Logic", func(t *testing.T) {
		testCases := []struct {
			name             string
			currentStreak    int
			expectedProgress int
			expectedTarget   int
			description      string
		}{
			{
				name:             "Day_0_No_Streak",
				currentStreak:    0,
				expectedProgress: 0,
				expectedTarget:   1,
				description:      "No streak yet, working toward 1-day milestone",
			},
			{
				name:             "Day_1_First_Milestone_Reached",
				currentStreak:    1,
				expectedProgress: 1,
				expectedTarget:   2,
				description:      "Completed 1-day milestone, now working toward 2-day milestone",
			},
			{
				name:             "Day_2_Second_Milestone_Reached",
				currentStreak:    2,
				expectedProgress: 1, // This is the key fix: should be 1/3, not 2/3
				expectedTarget:   3,
				description:      "Completed 2-day milestone, should show 1/3 toward 3-day milestone",
			},
			{
				name:             "Day_3_All_Milestones_Complete",
				currentStreak:    3,
				expectedProgress: 3,
				expectedTarget:   3,
				description:      "All milestones completed, show final milestone",
			},
			{
				name:             "Day_4_Beyond_All_Milestones",
				currentStreak:    4,
				expectedProgress: 3,
				expectedTarget:   3,
				description:      "Beyond all milestones, maintain highest milestone display",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Test progress calculation
				actualProgress := service.calculateMilestoneProgress(milestones, tc.currentStreak)
				assert.Equal(t, tc.expectedProgress, actualProgress,
					"Progress calculation failed for %s: %s", tc.name, tc.description)

				// Test target calculation
				nextMilestone := service.getNextMilestone(milestones, tc.currentStreak)
				var actualTarget int
				if nextMilestone != nil {
					actualTarget = nextMilestone.Days
				} else {
					// All milestones completed
					highestMilestone := service.getHighestMilestone(milestones)
					if highestMilestone != nil {
						actualTarget = highestMilestone.Days
					}
				}
				assert.Equal(t, tc.expectedTarget, actualTarget,
					"Target calculation failed for %s: %s", tc.name, tc.description)
			})
		}
	})

	t.Run("Edge_Cases", func(t *testing.T) {
		t.Run("Empty_Milestones", func(t *testing.T) {
			emptyMilestones := []model.ConsecutiveCheckinMilestone{}
			progress := service.calculateMilestoneProgress(emptyMilestones, 5)
			assert.Equal(t, 5, progress, "Should return streak count when no milestones defined")
		})

		t.Run("Single_Milestone", func(t *testing.T) {
			singleMilestone := []model.ConsecutiveCheckinMilestone{
				{Days: 7, Points: 500},
			}

			// Before milestone
			progress := service.calculateMilestoneProgress(singleMilestone, 3)
			assert.Equal(t, 3, progress, "Should show progress toward single milestone")

			// At milestone
			progress = service.calculateMilestoneProgress(singleMilestone, 7)
			assert.Equal(t, 7, progress, "Should show completion of single milestone")

			// Beyond milestone
			progress = service.calculateMilestoneProgress(singleMilestone, 10)
			assert.Equal(t, 7, progress, "Should maintain single milestone display")
		})

		t.Run("Non_Sequential_Milestones", func(t *testing.T) {
			nonSequentialMilestones := []model.ConsecutiveCheckinMilestone{
				{Days: 1, Points: 50},
				{Days: 5, Points: 200},
				{Days: 10, Points: 500},
			}

			testCases := []struct {
				streak   int
				expected int
			}{
				{0, 0},   // No streak
				{1, 1},   // Completed 1-day, working toward 5-day (1/5)
				{2, 1},   // Still working toward 5-day (2/5) -> should show 1 (2-1=1)
				{3, 2},   // Still working toward 5-day (3/5) -> should show 2 (3-1=2)
				{4, 3},   // Still working toward 5-day (4/5) -> should show 3 (4-1=3)
				{5, 1},   // Completed 5-day, working toward 10-day (1/10) -> should show 1 (5-5=0, but min 1)
				{6, 1},   // Working toward 10-day (6/10) -> should show 1 (6-5=1)
				{7, 2},   // Working toward 10-day (7/10) -> should show 2 (7-5=2)
				{10, 10}, // All completed
			}

			for _, tc := range testCases {
				progress := service.calculateMilestoneProgress(nonSequentialMilestones, tc.streak)
				assert.Equal(t, tc.expected, progress,
					"Non-sequential milestone progress failed for streak %d", tc.streak)
			}
		})
	})

	t.Run("Real_World_Scenario", func(t *testing.T) {
		// Test the exact scenario described in the issue
		realMilestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 200},
			{Days: 4, Points: 200},
		}

		// Day 1: Check in → Should show 1/1 (complete first milestone) → Advance to 1/2
		progress1 := service.calculateMilestoneProgress(realMilestones, 1)
		target1 := service.getNextMilestone(realMilestones, 1)
		assert.Equal(t, 1, progress1, "Day 1: Should show progress 1 toward next milestone")
		assert.Equal(t, 2, target1.Days, "Day 1: Should target 2-day milestone")

		// Day 2: Check in → Should show 1/4 (complete second milestone) → Advance to next
		progress2 := service.calculateMilestoneProgress(realMilestones, 2)
		target2 := service.getNextMilestone(realMilestones, 2)
		assert.Equal(t, 1, progress2, "Day 2: Should show progress 1 toward 4-day milestone (not 2)")
		assert.Equal(t, 4, target2.Days, "Day 2: Should target 4-day milestone")

		// Day 3: Check in → Should show 2/4 (working toward 4-day milestone)
		progress3 := service.calculateMilestoneProgress(realMilestones, 3)
		target3 := service.getNextMilestone(realMilestones, 3)
		assert.Equal(t, 1, progress3, "Day 3: Should show progress 1 toward 4-day milestone (3-2=1)")
		assert.Equal(t, 4, target3.Days, "Day 3: Should still target 4-day milestone")

		// Day 4: Check in → Should show 4/4 (complete all milestones)
		progress4 := service.calculateMilestoneProgress(realMilestones, 4)
		target4 := service.getNextMilestone(realMilestones, 4)
		assert.Equal(t, 4, progress4, "Day 4: Should show completion of all milestones")
		assert.Nil(t, target4, "Day 4: No more milestones to target")
	})
}

// TestMilestoneProgressionIntegration tests the integration with UpdateStreak
func TestMilestoneProgressionIntegration(t *testing.T) {
	t.Run("UpdateStreak_Integration", func(t *testing.T) {
		// This test verifies that the UpdateStreak method correctly uses the new logic
		// Note: This is a conceptual test since we can't fully test without database

		service := &TaskProgressService{}

		// Test that the calculateMilestoneProgress method exists and works
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 100},
		}

		// Test the key scenario: after completing 2-day milestone
		progress := service.calculateMilestoneProgress(milestones, 2)
		assert.Equal(t, 2, progress, "After completing 2-day milestone, should show completion")

		// Test working toward next milestone (if there was a 3-day milestone)
		milestonesWithThird := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 100},
			{Days: 3, Points: 150},
		}

		progressTowardThird := service.calculateMilestoneProgress(milestonesWithThird, 2)
		assert.Equal(t, 1, progressTowardThird, "After completing 2-day milestone, should show 1 toward 3-day milestone")
	})
}

// TestMilestoneProgressionEdgeCases tests additional edge cases
func TestMilestoneProgressionEdgeCases(t *testing.T) {
	service := &TaskProgressService{}

	t.Run("Zero_Streak_Handling", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
		}

		progress := service.calculateMilestoneProgress(milestones, 0)
		assert.Equal(t, 0, progress, "Zero streak should return 0 progress")
	})

	t.Run("Negative_Streak_Handling", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
		}

		// This shouldn't happen in practice, but test defensive programming
		progress := service.calculateMilestoneProgress(milestones, -1)
		assert.Equal(t, -1, progress, "Negative streak should be handled gracefully")
	})
}
