package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestConsecutiveCheckinLogic tests the consecutive check-in logic without database
func TestConsecutiveCheckinLogic(t *testing.T) {
	t.Run("Configurable_Task_Identifier_Defined", func(t *testing.T) {
		// Test that configurable consecutive check-in task identifier is properly defined
		assert.Equal(t, "CONSECUTIVE_CHECKIN_CONFIGURABLE", string(model.TaskIDConsecutiveCheckinConfigurable))
	})

	t.Run("Configurable_Task_Definition_Exists", func(t *testing.T) {
		// Test that configurable task definition exists in registry
		defConfigurable, exists := model.TaskDefinitionRegistry[model.TaskIDConsecutiveCheckinConfigurable]
		assert.True(t, exists, "Configurable consecutive check-in task definition should exist")
		assert.Equal(t, 0, defConfigurable.Points, "Configurable task should have 0 base points (determined by milestones)")
	})
}

// TestConsecutiveCheckinHandlerLogic tests the handler logic without database
func TestConsecutiveCheckinHandlerLogic(t *testing.T) {
	t.Run("Configurable_Handler_Exists", func(t *testing.T) {
		// Test that configurable handler type is properly defined
		service := &MockActivityCashbackService{}

		handlerConfigurable := NewConsecutiveCheckinConfigurableHandler(service)
		assert.NotNil(t, handlerConfigurable, "Configurable handler should be created")
		assert.Equal(t, model.TaskIDConsecutiveCheckinConfigurable, handlerConfigurable.identifier)
	})
}

// TestConfigurableMilestoneLogic tests that configurable milestones work correctly
func TestConfigurableMilestoneLogic(t *testing.T) {
	t.Run("Configurable_Milestones_Structure", func(t *testing.T) {
		// Test that configurable milestones can be defined
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 50},
			{Days: 7, Points: 200},
			{Days: 30, Points: 1000},
		}

		assert.Len(t, milestones, 3, "Should have 3 milestones")
		assert.Equal(t, 3, milestones[0].Days, "First milestone should be 3 days")
		assert.Equal(t, 50, milestones[0].Points, "First milestone should award 50 points")
	})
}

// TestTaskVisibilityLogic tests the task visibility logic
func TestTaskVisibilityLogic(t *testing.T) {
	ctx := context.Background()
	userID := uuid.New()

	service := NewActivityCashbackService()

	t.Run("Only_One_Task_Visible_At_Time", func(t *testing.T) {
		// Get all consecutive check-in tasks
		tasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
		require.NoError(t, err)

		var consecutiveTasks []model.ActivityTask
		for _, task := range tasks {
			if task.TaskIdentifier != nil {
				switch *task.TaskIdentifier {
				case model.TaskIDConsecutiveCheckinConfigurable:
					consecutiveTasks = append(consecutiveTasks, task)
				}
			}
		}

		if len(consecutiveTasks) == 0 {
			t.Skip("No consecutive check-in tasks found")
		}

		// Get task center data
		taskCenter, err := service.GetTaskCenter(ctx, userID)
		require.NoError(t, err)

		// Count visible consecutive check-in tasks
		visibleConsecutiveTasks := 0
		for _, category := range taskCenter.Categories {
			if category.Category.Name == model.CategoryDaily {
				for _, taskWithProgress := range category.Tasks {
					if taskWithProgress.Task.TaskIdentifier != nil {
						switch *taskWithProgress.Task.TaskIdentifier {
						case model.TaskIDConsecutiveCheckinConfigurable:
							visibleConsecutiveTasks++
						}
					}
				}
			}
		}

		// Should have exactly 1 configurable consecutive check-in task
		assert.Equal(t, 1, visibleConsecutiveTasks,
			"Should have exactly 1 configurable consecutive check-in task, got %d", visibleConsecutiveTasks)
	})
}

// TestConfigurableTaskProgression tests the configurable consecutive check-in task
func TestConfigurableTaskProgression(t *testing.T) {
	t.Run("Configurable_Task_Always_Available", func(t *testing.T) {
		// The configurable consecutive check-in task should always be available
		// It handles its own milestone logic internally

		// Test that the configurable task identifier exists
		assert.Equal(t, "CONSECUTIVE_CHECKIN_CONFIGURABLE", string(model.TaskIDConsecutiveCheckinConfigurable))

		// Test that milestones can be configured
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 50},
			{Days: 7, Points: 200},
			{Days: 30, Points: 1000},
		}

		assert.Len(t, milestones, 3, "Should support multiple milestones")
		assert.Equal(t, 3, milestones[0].Days, "First milestone should be configurable")
		assert.Equal(t, 50, milestones[0].Points, "First milestone points should be configurable")
	})
}
