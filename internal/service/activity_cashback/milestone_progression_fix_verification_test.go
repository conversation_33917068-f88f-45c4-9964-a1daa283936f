package activity_cashback

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestMilestoneProgressionFixVerification tests the specific issue described in the user's problem
func TestMilestoneProgressionFixVerification(t *testing.T) {
	service := &TaskProgressService{}

	// Test configuration from user's problem
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 1, Points: 10, Name: &model.MultilingualName{En: "1 days"}},
		{Days: 2, Points: 50, Name: &model.MultilingualName{En: "2 days"}},
		{Days: 3, Points: 100, Name: &model.MultilingualName{En: "3 days"}},
	}

	t.Run("User_Scenario_Fix_Verification", func(t *testing.T) {
		// Scenario: User has completed 1-day and 2-day milestones (completionCount = 2)
		// User has currentStreak = 3 (just checked in on day 3)
		// Expected: Should show 1/3 (working toward 3-day milestone)
		// NOT 3/3 (which was the bug)

		currentStreak := 3
		completionCount := 2 // User has completed 1-day and 2-day milestones

		progress := service.calculateMilestoneProgress(milestones, currentStreak, completionCount)

		// The user should see progress 1 toward the 3-day milestone
		// Because: currentStreak (3) - lastCompletedMilestone.Days (2) = 1
		assert.Equal(t, 1, progress, "After completing 2-day milestone, user with 3-day streak should show 1/3 progress toward 3-day milestone")
	})

	t.Run("Step_By_Step_Progression", func(t *testing.T) {
		// Day 1: User checks in, reaches 1-day milestone
		progress1_before := service.calculateMilestoneProgress(milestones, 1, 0)
		assert.Equal(t, 1, progress1_before, "Day 1 before completion: Should show 1/1")

		// Day 1: Milestone completed
		progress1_after := service.calculateMilestoneProgress(milestones, 1, 1)
		assert.Equal(t, 1, progress1_after, "Day 1 after completion: Should show 1/2 (working toward 2-day)")

		// Day 2: User checks in, reaches 2-day milestone
		progress2_before := service.calculateMilestoneProgress(milestones, 2, 1)
		assert.Equal(t, 1, progress2_before, "Day 2 before completion: Should show 1/2 (2-1=1)")

		// Day 2: Milestone completed
		progress2_after := service.calculateMilestoneProgress(milestones, 2, 2)
		assert.Equal(t, 1, progress2_after, "Day 2 after completion: Should show 1/3 (working toward 3-day)")

		// Day 3: User checks in (THIS WAS THE BUG SCENARIO)
		progress3_before := service.calculateMilestoneProgress(milestones, 3, 2)
		assert.Equal(t, 1, progress3_before, "Day 3 before completion: Should show 1/3 (3-2=1), NOT 3/3")

		// Day 3: Milestone completed
		progress3_after := service.calculateMilestoneProgress(milestones, 3, 3)
		assert.Equal(t, 3, progress3_after, "Day 3 after completion: Should show 3/3 (all milestones completed)")
	})

	t.Run("Edge_Cases", func(t *testing.T) {
		// No milestones completed yet
		progress := service.calculateMilestoneProgress(milestones, 1, 0)
		assert.Equal(t, 1, progress, "No milestones completed: Should show current streak")

		// All milestones completed
		progress = service.calculateMilestoneProgress(milestones, 5, 3)
		assert.Equal(t, 3, progress, "All milestones completed: Should show highest milestone")

		// Completion count exceeds milestones (defensive)
		progress = service.calculateMilestoneProgress(milestones, 10, 5)
		assert.Equal(t, 3, progress, "Completion count exceeds milestones: Should show highest milestone")
	})
}
