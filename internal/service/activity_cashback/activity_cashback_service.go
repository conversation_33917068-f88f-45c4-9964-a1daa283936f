package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ActivityCashbackService implements ActivityCashbackServiceInterface
type ActivityCashbackService struct {
	TaskManagementServiceInterface
	TierManagementServiceInterface
	TaskProgressServiceInterface
	CashbackClaimServiceInterface

	categoryRepo    activity_cashback.TaskCategoryRepositoryInterface
	tierBenefitRepo activity_cashback.TierBenefitRepositoryInterface
	taskRegistry    *TaskRegistry // Add task registry for handler-based task processing
}

// NewActivityCashbackService creates a new ActivityCashbackService
func NewActivityCashbackService() ActivityCashbackServiceInterface {
	// Initialize repositories
	categoryRepo := activity_cashback.NewTaskCategoryRepository()
	taskRepo := activity_cashback.NewActivityTaskRepository()
	progressRepo := activity_cashback.NewUserTaskProgressRepository()
	tierInfoRepo := activity_cashback.NewUserTierInfoRepository()
	tierBenefitRepo := activity_cashback.NewTierBenefitRepository()

	claimRepo := activity_cashback.NewActivityCashbackClaimRepository()

	// Initialize services
	tierService := NewTierManagementService(tierInfoRepo, tierBenefitRepo)
	progressService := NewTaskProgressService(progressRepo, taskRepo)
	taskService := NewTaskManagementService(taskRepo, categoryRepo, progressRepo, tierService, progressService)
	claimService := NewCashbackClaimService(claimRepo)

	service := &ActivityCashbackService{
		TaskManagementServiceInterface: taskService,
		TierManagementServiceInterface: tierService,
		TaskProgressServiceInterface:   progressService,
		CashbackClaimServiceInterface:  claimService,
		categoryRepo:                   categoryRepo,
		tierBenefitRepo:                tierBenefitRepo,
	}

	// Initialize task registry with the service
	service.taskRegistry = NewTaskRegistry(service)

	return service
}

// InitializeUserForActivityCashback initializes a user for the activity cashback system
func (s *ActivityCashbackService) InitializeUserForActivityCashback(ctx context.Context, userID uuid.UUID) error {
	// First, ensure the user record exists in the database
	if err := s.ensureUserExists(ctx, userID); err != nil {
		return fmt.Errorf("failed to ensure user exists: %w", err)
	}

	// Create user tier info if not exists
	_, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			if _, err := s.CreateUserTierInfo(ctx, userID); err != nil {
				return fmt.Errorf("failed to create user tier info: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get user tier info: %w", err)
		}
	}

	// Initialize task progress for available tasks
	if err := s.RefreshUserTasks(ctx, userID); err != nil {
		return fmt.Errorf("failed to refresh user tasks: %w", err)
	}

	global.GVA_LOG.Info("User initialized for activity cashback", zap.String("user_id", userID.String()))
	return nil
}

// ensureUserExists ensures that a user record exists in the database
// This handles the case where users have valid JWT tokens but haven't been synced via NATS
func (s *ActivityCashbackService) ensureUserExists(ctx context.Context, userID uuid.UUID) error {
	// Use database transaction to handle race conditions
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// Check if user already exists
		var existingUser model.User
		err := tx.WithContext(ctx).Where("id = ?", userID).First(&existingUser).Error

		if err == nil {
			// User already exists, nothing to do
			global.GVA_LOG.Debug("User already exists in database",
				zap.String("user_id", userID.String()))
			return nil
		}

		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check if user exists: %w", err)
		}

		// User doesn't exist, create new one with default settings
		// This mirrors the logic from dex_user_subscriber.go
		newUser := &model.User{
			ID:             userID,
			AgentLevelID:   1,   // Default to Level 1
			Email:          nil, // Will be set later if available from JWT claims
			InvitationCode: nil, // Will be set later if user creates one
		}

		if err := tx.WithContext(ctx).Create(newUser).Error; err != nil {
			return fmt.Errorf("failed to create user: %w", err)
		}

		// Create referral snapshot for the new user
		snapshot := model.ReferralSnapshot{
			UserID:             userID,
			DirectCount:        0,
			TotalDownlineCount: 0,
		}

		if err := tx.WithContext(ctx).Create(&snapshot).Error; err != nil {
			return fmt.Errorf("failed to create referral snapshot: %w", err)
		}

		global.GVA_LOG.Info("Successfully created user record for Activity Cashback",
			zap.String("user_id", userID.String()),
			zap.String("reason", "user_had_jwt_but_not_synced_via_nats"))

		return nil
	})
}

// GetUserDashboard retrieves user dashboard data
func (s *ActivityCashbackService) GetUserDashboard(ctx context.Context, userID uuid.UUID) (*UserDashboard, error) {
	// Get user tier info
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	// Get current tier benefit
	tierBenefit, err := s.tierBenefitRepo.GetByTierLevel(ctx, tierInfo.CurrentTier)
	if err != nil {
		return nil, fmt.Errorf("failed to get tier benefit: %w", err)
	}

	// Get next tier and points needed
	nextTier, pointsToNextTier, err := s.GetNextTierRequirement(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get next tier requirement: %w", err)
	}

	// Get claimable cashback
	claimableCashback, err := s.GetClaimableCashback(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get claimable cashback: %w", err)
	}

	// Get recent claims
	recentClaims, err := s.GetUserClaims(ctx, userID, 5, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent claims: %w", err)
	}

	// Get user rank
	userRank, err := s.GetUserRank(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user rank", zap.Error(err))
		userRank = 0 // Default to 0 if failed
	}

	dashboard := &UserDashboard{
		UserTierInfo:      tierInfo,
		TierBenefit:       tierBenefit,
		NextTier:          nextTier,
		PointsToNextTier:  pointsToNextTier,
		ClaimableCashback: claimableCashback,
		RecentClaims:      recentClaims,
		UserRank:          userRank,
	}

	return dashboard, nil
}

// GetTaskCenter retrieves task center data
func (s *ActivityCashbackService) GetTaskCenter(ctx context.Context, userID uuid.UUID) (*TaskCenter, error) {
	// Get all categories with tasks
	categories, err := s.categoryRepo.GetActive(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	// Get user progress
	userProgress, err := s.GetUserTaskProgress(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user progress: %w", err)
	}

	// Create progress map for quick lookup
	progressMap := make(map[uuid.UUID]*model.UserTaskProgress)
	for i := range userProgress {
		progressMap[userProgress[i].TaskID] = &userProgress[i]
	}

	// Build categories with tasks and progress
	var categoriesWithTasks []TaskCategoryWithTasks
	for _, category := range categories {
		tasks, err := s.GetTasksByCategory(ctx, category.Name)
		if err != nil {
			global.GVA_LOG.Error("Failed to get tasks for category", zap.Error(err), zap.String("category", string(category.Name)))
			continue
		}

		var tasksWithProgress []TaskWithProgress
		for _, task := range tasks {
			// Filter consecutive check-in tasks - only show the appropriate one
			if s.isConsecutiveCheckinTask(task) {
				visibleTask, err := s.GetVisibleConsecutiveCheckinTask(ctx, userID)
				if err != nil {
					global.GVA_LOG.Error("Failed to get visible consecutive check-in task", zap.Error(err))
					continue
				}
				if visibleTask == nil || visibleTask.ID != task.ID {
					continue // Skip this task as it's not the visible one
				}

				// Enhance consecutive check-in task with milestone information
				task = s.enhanceConsecutiveCheckinTask(ctx, task, userID, "en") // Default to English for now
			}

			taskWithProgress := TaskWithProgress{
				Task:     task,
				Progress: progressMap[task.ID],
			}
			tasksWithProgress = append(tasksWithProgress, taskWithProgress)
		}

		categoryWithTasks := TaskCategoryWithTasks{
			Category: category,
			Tasks:    tasksWithProgress,
		}
		categoriesWithTasks = append(categoriesWithTasks, categoryWithTasks)
	}

	// Calculate today's stats
	today := time.Now().Truncate(24 * time.Hour)
	completedToday := 0
	pointsEarnedToday := 0

	for _, progress := range userProgress {
		if progress.LastCompletedAt != nil && progress.LastCompletedAt.Truncate(24*time.Hour).Equal(today) {
			completedToday++
			pointsEarnedToday += progress.Task.Points
		}
	}

	// Get streak tasks
	streakTasks, err := s.GetUserStreaks(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user streaks", zap.Error(err))
		streakTasks = []model.UserTaskProgress{}
	}

	taskCenter := &TaskCenter{
		Categories:        categoriesWithTasks,
		UserProgress:      userProgress,
		CompletedToday:    completedToday,
		PointsEarnedToday: pointsEarnedToday,
		StreakTasks:       streakTasks,
	}

	return taskCenter, nil
}

// GetActivityCashbackSummary retrieves optimized summary data for frontend UI
func (s *ActivityCashbackService) GetActivityCashbackSummary(ctx context.Context, userID uuid.UUID) (*ActivityCashbackSummary, error) {
	// Get user tier info
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	// Get current tier benefit
	currentTierBenefit, err := s.tierBenefitRepo.GetByTierLevel(ctx, tierInfo.CurrentTier)
	if err != nil {
		return nil, fmt.Errorf("failed to get current tier benefit: %w", err)
	}

	// Get next tier and points needed
	nextTier, pointsToNextTier, err := s.GetNextTierRequirement(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get next tier requirement: %w", err)
	}

	// Calculate progress percentage
	progressPercentage := 0.0
	var totalScoreForNextLevel *int
	var scoreRequiredToUpgrade *int
	var nextLevel *int
	var nextLevelName *string
	var nextTierColor *string
	var nextTierIcon *string

	if nextTier != nil {
		nextLevelInt := nextTier.TierLevel
		nextLevel = &nextLevelInt
		nextLevelName = &nextTier.TierName
		nextTierColor = nextTier.TierColor
		nextTierIcon = nextTier.TierIcon

		totalScoreForNextLevel = &nextTier.MinPoints
		scoreRequiredToUpgrade = &pointsToNextTier

		// Calculate progress percentage
		currentProgress := tierInfo.TotalPoints - currentTierBenefit.MinPoints
		totalNeeded := nextTier.MinPoints - currentTierBenefit.MinPoints
		if totalNeeded > 0 {
			progressPercentage = float64(currentProgress) / float64(totalNeeded) * 100.0
			if progressPercentage > 100.0 {
				progressPercentage = 100.0
			}
		}
	} else {
		// User is at max tier
		progressPercentage = 100.0
	}

	// Convert decimal values to float64 for GraphQL
	accumulatedTradingVolumeUSD := tierInfo.TradingVolumeUSD
	accumulatedCashbackUSD := tierInfo.CumulativeCashbackUSD
	claimableCashbackUSD := tierInfo.ClaimableCashbackUSD
	claimedCashbackUSD := tierInfo.ClaimedCashbackUSD

	summary := &ActivityCashbackSummary{
		// Current ranking info
		CurrentLevel:     tierInfo.CurrentTier,
		CurrentLevelName: currentTierBenefit.TierName,
		NextLevel:        nextLevel,
		NextLevelName:    nextLevelName,

		// Progress calculation
		CurrentScore:           tierInfo.TotalPoints,
		TotalScoreForNextLevel: totalScoreForNextLevel,
		ScoreRequiredToUpgrade: scoreRequiredToUpgrade,
		ProgressPercentage:     progressPercentage,

		// Trading volume (MEME only - Derivatives excluded from Activity Cashback)
		AccumulatedTradingVolumeUSD: accumulatedTradingVolumeUSD,

		// Activity tracking
		ActiveLogonDays: tierInfo.ActiveDaysThisMonth,

		// Cashback information
		AccumulatedCashbackUSD: accumulatedCashbackUSD,
		ClaimableCashbackUSD:   claimableCashbackUSD,
		ClaimedCashbackUSD:     claimedCashbackUSD,

		// Additional tier info
		CurrentTierColor: currentTierBenefit.TierColor,
		CurrentTierIcon:  currentTierBenefit.TierIcon,
		NextTierColor:    nextTierColor,
		NextTierIcon:     nextTierIcon,
	}

	return summary, nil
}

// isConsecutiveCheckinTask checks if a task is a consecutive check-in task
func (s *ActivityCashbackService) isConsecutiveCheckinTask(task model.ActivityTask) bool {
	if task.TaskIdentifier == nil {
		return false
	}
	switch *task.TaskIdentifier {
	case model.TaskIDConsecutiveCheckinConfigurable:
		return true
	default:
		return false
	}
}

// GetVisibleConsecutiveCheckinTask delegates to embedded TaskManagementServiceInterface
func (s *ActivityCashbackService) GetVisibleConsecutiveCheckinTask(ctx context.Context, userID uuid.UUID) (*model.ActivityTask, error) {
	return s.TaskManagementServiceInterface.GetVisibleConsecutiveCheckinTask(ctx, userID)
}

// enhanceConsecutiveCheckinTask enhances a consecutive check-in task with milestone-specific display information
func (s *ActivityCashbackService) enhanceConsecutiveCheckinTask(ctx context.Context, task model.ActivityTask, userID uuid.UUID, preferredLang string) model.ActivityTask {
	// Only enhance configurable consecutive check-in tasks
	if task.TaskIdentifier == nil || *task.TaskIdentifier != model.TaskIDConsecutiveCheckinConfigurable {
		return task
	}

	// Get user's current progress
	progress, err := s.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get task progress for milestone enhancement",
			zap.Error(err),
			zap.String("task_id", task.ID.String()),
			zap.String("user_id", userID.String()))
		return task
	}

	// Get milestones from task conditions
	if task.Conditions == nil || len(task.Conditions.ConsecutiveCheckinMilestones) == 0 {
		return task
	}

	// Get milestone display information
	currentStreak := 0
	if progress != nil {
		currentStreak = progress.StreakCount
	}

	milestoneInfo := GetMilestoneDisplayInfo(task.Conditions.ConsecutiveCheckinMilestones, currentStreak, preferredLang)
	if milestoneInfo == nil {
		return task
	}

	// Create a copy of the task to avoid modifying the original
	enhancedTask := task

	// Get the original multilingual name
	originalName := enhancedTask.GetMultilingualName()
	if originalName == nil {
		// Fallback to legacy name field
		originalName = &model.MultilingualName{
			En: enhancedTask.Name,
		}
	}

	// Format the task name with milestone information
	enhancedName := FormatTaskNameWithMilestone(originalName, milestoneInfo, preferredLang)
	enhancedTask.SetMultilingualName(enhancedName)

	// Update points to show the next achievable milestone points
	dynamicPoints := s.calculateDynamicMilestonePoints(task.Conditions.ConsecutiveCheckinMilestones, currentStreak)
	enhancedTask.Points = dynamicPoints

	// Update description with progress information if next milestone exists
	if milestoneInfo.NextMilestone != nil && !milestoneInfo.IsCompleted {
		current, total, percentage := GetMilestoneProgress(task.Conditions.ConsecutiveCheckinMilestones, currentStreak)
		progressDesc := fmt.Sprintf("Progress: %d/%d days (%.1f%%)", current, total, percentage)

		if enhancedTask.Description != nil {
			enhancedTask.Description = &[]string{fmt.Sprintf("%s\n%s", *enhancedTask.Description, progressDesc)}[0]
		} else {
			enhancedTask.Description = &progressDesc
		}
	}

	return enhancedTask
}

// calculateDynamicMilestonePoints calculates the points to display for a consecutive check-in task
// based on the user's current progress and next achievable milestone
func (s *ActivityCashbackService) calculateDynamicMilestonePoints(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) int {
	if len(milestones) == 0 {
		return 0
	}

	// Get the next milestone the user is working towards
	nextMilestone := GetNextMilestone(milestones, currentStreak)
	if nextMilestone != nil {
		// Show points for the next achievable milestone
		return nextMilestone.Points
	}

	// If no next milestone (all completed), show points from the highest milestone
	highestMilestone := s.getHighestMilestone(milestones)
	if highestMilestone != nil {
		return highestMilestone.Points
	}

	// Fallback to 0 if no milestones found
	return 0
}

// getHighestMilestone returns the milestone with the highest day requirement
func (s *ActivityCashbackService) getHighestMilestone(milestones []model.ConsecutiveCheckinMilestone) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	highest := &milestones[0]
	for i := 1; i < len(milestones); i++ {
		if milestones[i].Days > highest.Days {
			highest = &milestones[i]
		}
	}
	return highest
}

// RefreshTaskList refreshes the task list for a user
func (s *ActivityCashbackService) RefreshTaskList(ctx context.Context, userID uuid.UUID) error {
	return s.RefreshUserTasks(ctx, userID)
}

// GetUserTaskListByCategoryWithDetails retrieves user task progress by category with full task details
func (s *ActivityCashbackService) GetUserTaskListByCategoryWithDetails(ctx context.Context, userID uuid.UUID, categoryName model.TaskCategoryName) ([]TaskWithProgress, error) {
	// Get tasks by category
	tasks, err := s.GetTasksByCategory(ctx, categoryName)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Get user progress for all tasks
	userProgress, err := s.GetUserTaskProgress(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress: %w", err)
	}

	// Create progress map for quick lookup
	progressMap := make(map[uuid.UUID]*model.UserTaskProgress)
	for i := range userProgress {
		progressMap[userProgress[i].TaskID] = &userProgress[i]
	}

	// Build tasks with progress
	var tasksWithProgress []TaskWithProgress
	for _, task := range tasks {
		// Filter consecutive check-in tasks - only show the appropriate one
		if s.isConsecutiveCheckinTask(task) {
			visibleTask, err := s.GetVisibleConsecutiveCheckinTask(ctx, userID)
			if err != nil {
				global.GVA_LOG.Error("Failed to get visible consecutive check-in task", zap.Error(err))
				continue
			}
			if visibleTask == nil || visibleTask.ID != task.ID {
				continue // Skip this task as it's not the visible one
			}

			// Enhance consecutive check-in task with milestone information
			task = s.enhanceConsecutiveCheckinTask(ctx, task, userID, "en") // Default to English for now
		}

		taskWithProgress := TaskWithProgress{
			Task:     task,
			Progress: progressMap[task.ID],
		}
		tasksWithProgress = append(tasksWithProgress, taskWithProgress)
	}

	return tasksWithProgress, nil
}

// CompleteTaskWithPoints delegates to TaskManagementService to ensure points are awarded
func (s *ActivityCashbackService) CompleteTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Cast to concrete type to access CompleteTaskWithPoints method
	if taskMgmtService, ok := s.TaskManagementServiceInterface.(*TaskManagementService); ok {
		return taskMgmtService.CompleteTaskWithPoints(ctx, userID, taskID, verificationData)
	}
	return fmt.Errorf("unable to cast TaskManagementServiceInterface to concrete type")
}

// ProcessTaskWithRegistry processes a task using the task registry system
// This method should be used for tasks that have specific handlers (like SHARE_EARNINGS_CHART)
func (s *ActivityCashbackService) ProcessTaskWithRegistry(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	if s.taskRegistry == nil {
		return fmt.Errorf("task registry not initialized")
	}
	return s.taskRegistry.ProcessTask(ctx, userID, task, data)
}

// AddTradingVolume adds trading volume to user's accumulated total
func (s *ActivityCashbackService) AddTradingVolume(ctx context.Context, userID uuid.UUID, volume decimal.Decimal) error {
	return s.TierManagementServiceInterface.AddTradingVolume(ctx, userID, volume)
}
