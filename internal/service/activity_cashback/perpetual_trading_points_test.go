package activity_cashback

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestPerpetualTradingPointsCalculation tests the fixed PERPETUAL trading points calculation
func TestPerpetualTradingPointsCalculation(t *testing.T) {
	tests := []struct {
		name           string
		volume         float64
		tradeType      string
		expectedPoints int
		description    string
	}{
		// PERPETUAL trades - direct calculation (volume/95)
		{
			name:           "PERPETUAL User Example",
			volume:         117104.00 * 0.00256, // AvgPrice * Size = 299.78624
			tradeType:      "PERPETUAL",
			expectedPoints: 3, // (299.78624 / 95) = 3.155... -> 3 points
			description:    "User's exact example: should award 3 points, not 1",
		},
		{
			name:           "PERPETUAL Small Volume",
			volume:         95.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 1, // (95 / 95) = 1 point
			description:    "Exactly 95 volume should give 1 point",
		},
		{
			name:           "PERPETUAL Medium Volume",
			volume:         950.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 10, // (950 / 95) = 10 points
			description:    "950 volume should give 10 points",
		},
		{
			name:           "PERPETUAL Large Volume",
			volume:         9500.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 100, // (9500 / 95) = 100 points
			description:    "9500 volume should give 100 points",
		},
		{
			name:           "PERPETUAL Fractional Result",
			volume:         190.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 2, // (190 / 95) = 2.0 points
			description:    "190 volume should give exactly 2 points",
		},
		{
			name:           "PERPETUAL Fractional Truncated",
			volume:         285.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 3, // (285 / 95) = 3.0 points
			description:    "285 volume should give exactly 3 points",
		},
		{
			name:           "PERPETUAL Very Small Volume",
			volume:         50.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 0, // (50 / 95) = 0.526... -> 0 points
			description:    "Small volume should give 0 points when result < 1",
		},

		// MEME trades - tier-based system (should remain unchanged)
		{
			name:           "MEME Tier 1",
			volume:         1.0,
			tradeType:      "MEME",
			expectedPoints: 1,
			description:    "MEME $1 should give 1 point (tier system)",
		},
		{
			name:           "MEME Tier 2",
			volume:         100.0,
			tradeType:      "MEME",
			expectedPoints: 5,
			description:    "MEME $100 should give 5 points (tier system)",
		},
		{
			name:           "MEME Tier 3",
			volume:         500.0,
			tradeType:      "MEME",
			expectedPoints: 12,
			description:    "MEME $500 should give 12 points (tier system)",
		},
		{
			name:           "MEME Tier 4",
			volume:         3000.0,
			tradeType:      "MEME",
			expectedPoints: 25,
			description:    "MEME $3000 should give 25 points (tier system)",
		},
		{
			name:           "MEME Tier 5",
			volume:         10000.0,
			tradeType:      "MEME",
			expectedPoints: 40,
			description:    "MEME $10000 should give 40 points (tier system)",
		},

		// Edge cases
		{
			name:           "PERPETUAL Zero Volume",
			volume:         0.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 0,
			description:    "Zero volume should give 0 points",
		},
		{
			name:           "MEME Zero Volume",
			volume:         0.0,
			tradeType:      "MEME",
			expectedPoints: 0,
			description:    "Zero volume should give 0 points",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var points int

			// Apply the fixed calculation logic
			switch tt.tradeType {
			case "MEME":
				// MEME trades: tier-based conversion (unchanged)
				switch {
				case tt.volume >= 10000:
					points = 40
				case tt.volume >= 3000:
					points = 25
				case tt.volume >= 500:
					points = 12
				case tt.volume >= 100:
					points = 5
				case tt.volume >= 1:
					points = 1
				default:
					points = 0
				}
			case "PERPETUAL":
				// PERPETUAL trades: direct calculation (fixed)
				directPoints := tt.volume / 95.0
				points = int(directPoints)
				if points < 0 {
					points = 0
				}
			default:
				points = 0
			}

			assert.Equal(t, tt.expectedPoints, points,
				"Points calculation mismatch for %s: %s",
				tt.name, tt.description)

			t.Logf("✓ %s: Volume=%.2f, Type=%s, Expected=%d, Got=%d - %s",
				tt.name, tt.volume, tt.tradeType, tt.expectedPoints, points, tt.description)
		})
	}
}

// TestPerpetualVsMemeComparison tests the difference between PERPETUAL and MEME calculations
func TestPerpetualVsMemeComparison(t *testing.T) {
	testVolume := 299.78624 // User's example volume

	// MEME calculation (tier-based)
	var memePoints int
	switch {
	case testVolume >= 10000:
		memePoints = 40
	case testVolume >= 3000:
		memePoints = 25
	case testVolume >= 500:
		memePoints = 12
	case testVolume >= 100:
		memePoints = 5
	case testVolume >= 1:
		memePoints = 1
	default:
		memePoints = 0
	}

	// PERPETUAL calculation (direct)
	directPoints := testVolume / 95.0
	perpetualPoints := int(directPoints)
	if perpetualPoints < 0 {
		perpetualPoints = 0
	}

	t.Logf("Volume: %.5f", testVolume)
	t.Logf("MEME points (tier-based): %d", memePoints)
	t.Logf("PERPETUAL points (direct): %d (calculated: %.5f)", perpetualPoints, directPoints)

	// Verify the fix
	assert.Equal(t, 5, memePoints, "MEME should give 5 points (tier system: $299 falls in $100-499 tier)")
	assert.Equal(t, 3, perpetualPoints, "PERPETUAL should give 3 points (direct calculation)")
	assert.NotEqual(t, memePoints, perpetualPoints, "PERPETUAL and MEME should calculate differently")
}

// TestUserExampleSpecific tests the exact user example with detailed verification
func TestUserExampleSpecific(t *testing.T) {
	// User's exact example data
	avgPrice := 117104.00
	size := 0.00256
	volume := avgPrice * size // = 299.78624

	t.Logf("User Example:")
	t.Logf("  AvgPrice: $%.2f", avgPrice)
	t.Logf("  Size: %.5f", size)
	t.Logf("  Volume: $%.5f", volume)

	// Before fix (old logic): volume/95 = 3.155, but tier system gives 1 point
	oldCalculation := volume / 95.0
	oldTierPoints := 1 // Because 3.155 falls in $1-99 tier

	// After fix (new logic): volume/95 = 3.155, direct conversion gives 3 points
	newCalculation := volume / 95.0
	newDirectPoints := int(newCalculation) // = 3

	t.Logf("Calculation Details:")
	t.Logf("  Raw calculation: %.5f / 95 = %.5f", volume, oldCalculation)
	t.Logf("  Old result (tier-based): %d points", oldTierPoints)
	t.Logf("  New result (direct): %d points", newDirectPoints)

	// Verify the fix
	assert.Equal(t, 3, newDirectPoints, "PERPETUAL trade should award 3 points (direct calculation)")
	assert.NotEqual(t, oldTierPoints, newDirectPoints, "Fix should change the result from 1 to 3 points")

	// Verify the calculation is correct
	expectedCalculation := 3.155644736842105 // Exact calculation
	assert.InDelta(t, expectedCalculation, newCalculation, 0.000001, "Calculation should be accurate")
}
