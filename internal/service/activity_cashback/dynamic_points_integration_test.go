package activity_cashback

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestDynamicPointsIntegration tests the complete integration of dynamic points in userTaskListByCategory
func TestDynamicPointsIntegration(t *testing.T) {
	t.Run("Test_Enhanced_Task_Points_Display", func(t *testing.T) {
		// This test verifies that the enhanceConsecutiveCheckinTask method
		// correctly updates task points based on user progress

		userID := uuid.New()

		// Create a test task with milestones matching the user's example
		task := model.ActivityTask{
			ID:             uuid.New(),
			TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDConsecutiveCheckinConfigurable}[0],
			Name:           "Custom Consecutive Check-in",
			Points:         0, // Base task has 0 points
			Conditions: &model.TaskConditions{
				ConsecutiveCheckinMilestones: []model.ConsecutiveCheckinMilestone{
					{Days: 1, Points: 50, Name: &model.MultilingualName{En: "Daily 1"}},
					{Days: 2, Points: 200, Name: &model.MultilingualName{En: "Daily 2"}},
					{Days: 4, Points: 200, Name: &model.MultilingualName{En: "Daily 4"}},
				},
			},
		}

		// Test scenarios with different user progress states
		testScenarios := []struct {
			name           string
			userStreak     int
			expectedPoints int
			description    string
		}{
			{
				name:           "New_User_No_Streak",
				userStreak:     0,
				expectedPoints: 50,
				description:    "New user should see points for 1-day milestone",
			},
			{
				name:           "User_With_One_Day_Streak",
				userStreak:     1,
				expectedPoints: 200,
				description:    "User with 1-day streak should see points for 2-day milestone",
			},
			{
				name:           "User_With_Two_Day_Streak",
				userStreak:     2,
				expectedPoints: 200,
				description:    "User with 2-day streak should see points for 4-day milestone",
			},
			{
				name:           "User_With_Three_Day_Streak",
				userStreak:     3,
				expectedPoints: 200,
				description:    "User with 3-day streak should see points for 4-day milestone",
			},
			{
				name:           "User_Completed_All_Milestones",
				userStreak:     4,
				expectedPoints: 200,
				description:    "User who completed all milestones should see highest milestone points",
			},
		}

		for _, scenario := range testScenarios {
			t.Run(scenario.name, func(t *testing.T) {
				// Create mock progress for the user
				mockProgress := &model.UserTaskProgress{
					UserID:      userID,
					TaskID:      task.ID,
					StreakCount: scenario.userStreak,
				}

				// Mock the GetTaskProgress method by creating a temporary service
				// In a real integration test, this would use actual database data
				testService := &testActivityCashbackService{
					mockProgress: mockProgress,
				}

				// Test the dynamic points calculation directly
				actualPoints := testService.calculateDynamicMilestonePoints(
					task.Conditions.ConsecutiveCheckinMilestones,
					scenario.userStreak,
				)

				assert.Equal(t, scenario.expectedPoints, actualPoints, scenario.description)

				// Verify the logic matches the expected behavior from the user's request
				if scenario.userStreak == 1 {
					assert.Equal(t, 200, actualPoints,
						"User with 1-day streak should see 200 points (2-day milestone) as per user's example")
				}
			})
		}
	})

	t.Run("Test_Task_Enhancement_Flow", func(t *testing.T) {
		// Test the complete task enhancement flow
		service := &ActivityCashbackService{}

		// Create test milestones
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 200},
			{Days: 4, Points: 200},
		}

		// Test the calculateDynamicMilestonePoints method with various streaks
		testCases := []struct {
			streak         int
			expectedPoints int
		}{
			{0, 50},   // No streak -> 1-day milestone (50 points)
			{1, 200},  // 1-day streak -> 2-day milestone (200 points)
			{2, 200},  // 2-day streak -> 4-day milestone (200 points)
			{3, 200},  // 3-day streak -> 4-day milestone (200 points)
			{4, 200},  // 4+ day streak -> highest milestone (200 points)
			{10, 200}, // Way beyond -> highest milestone (200 points)
		}

		for _, tc := range testCases {
			actualPoints := service.calculateDynamicMilestonePoints(milestones, tc.streak)
			assert.Equal(t, tc.expectedPoints, actualPoints,
				"Streak %d should show %d points, got %d", tc.streak, tc.expectedPoints, actualPoints)
		}
	})

	t.Run("Test_API_Response_Structure", func(t *testing.T) {
		// Test that the API response structure supports dynamic points
		// This verifies that the TaskWithProgress structure can handle dynamic points

		task := model.ActivityTask{
			ID:     uuid.New(),
			Name:   "Test Task",
			Points: 0, // Original points
		}

		progress := &model.UserTaskProgress{
			UserID:      uuid.New(),
			TaskID:      task.ID,
			StreakCount: 1,
		}

		// Create TaskWithProgress structure (as returned by userTaskListByCategory)
		taskWithProgress := TaskWithProgress{
			Task:     task,
			Progress: progress,
		}

		// Verify the structure
		assert.Equal(t, task.ID, taskWithProgress.Task.ID)
		assert.Equal(t, progress.StreakCount, taskWithProgress.Progress.StreakCount)
		assert.Equal(t, 0, taskWithProgress.Task.Points) // Original points before enhancement

		// After enhancement, the task points should be updated
		// (This would happen in the enhanceConsecutiveCheckinTask method)
		enhancedTask := task
		enhancedTask.Points = 200 // Simulated enhancement

		enhancedTaskWithProgress := TaskWithProgress{
			Task:     enhancedTask,
			Progress: progress,
		}

		assert.Equal(t, 200, enhancedTaskWithProgress.Task.Points,
			"Enhanced task should show dynamic points")
	})
}

// testActivityCashbackService is a test helper that extends ActivityCashbackService
// for testing purposes
type testActivityCashbackService struct {
	ActivityCashbackService
	mockProgress *model.UserTaskProgress
}

// calculateDynamicMilestonePoints delegates to the real implementation
func (s *testActivityCashbackService) calculateDynamicMilestonePoints(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) int {
	return s.ActivityCashbackService.calculateDynamicMilestonePoints(milestones, currentStreak)
}

// TestUserExampleScenario tests the exact scenario described in the user's request
func TestUserExampleScenario(t *testing.T) {
	t.Run("Exact_User_Example", func(t *testing.T) {
		service := &ActivityCashbackService{}

		// User's exact configuration
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 200},
			{Days: 4, Points: 200},
		}

		// User's current state: streakCount: 1, targetValue: 2 (working towards 2-day milestone)
		currentStreak := 1
		expectedPoints := 200 // Points for the 2-day milestone they're working towards

		actualPoints := service.calculateDynamicMilestonePoints(milestones, currentStreak)

		assert.Equal(t, expectedPoints, actualPoints,
			"User with streakCount: 1 should see 200 points (2-day milestone)")

		// Verify this matches the user's expectation
		assert.Equal(t, 200, actualPoints,
			"Expected points display: 200 (points for the 2-day milestone they're working towards)")
	})

	t.Run("API_Response_Behavior", func(t *testing.T) {
		// Test the expected API response behavior

		// Before enhancement: task shows 0 points (base task points)
		baseTaskPoints := 0

		// After enhancement: task should show dynamic points based on user progress
		service := &ActivityCashbackService{}
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 200},
			{Days: 4, Points: 200},
		}

		userStreak := 1
		dynamicPoints := service.calculateDynamicMilestonePoints(milestones, userStreak)

		// Verify the transformation
		assert.Equal(t, 0, baseTaskPoints, "Base task should have 0 points")
		assert.Equal(t, 200, dynamicPoints, "Dynamic points should be 200 for user with 1-day streak")
		assert.NotEqual(t, baseTaskPoints, dynamicPoints, "Dynamic points should differ from base points")

		// This represents what the API should return
		expectedAPIResponse := map[string]interface{}{
			"task": map[string]interface{}{
				"points": dynamicPoints, // 200 instead of 0
			},
			"progress": map[string]interface{}{
				"streakCount": userStreak, // 1
				"targetValue": 2,          // Working towards 2-day milestone
			},
		}

		assert.Equal(t, 200, expectedAPIResponse["task"].(map[string]interface{})["points"])
		assert.Equal(t, 1, expectedAPIResponse["progress"].(map[string]interface{})["streakCount"])
		assert.Equal(t, 2, expectedAPIResponse["progress"].(map[string]interface{})["targetValue"])
	})
}
