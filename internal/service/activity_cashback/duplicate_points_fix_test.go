package activity_cashback

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// TestPerpetualTradeDuplicatePointsFix tests that PERPETUAL trades only award points once
// This test verifies the fix for the issue where PERPETUAL trades were awarding points twice:
// 1. Once through TradingPerpetualTradeHandler.CompleteTaskWithPoints()
// 2. Once through TradingPointsHandler.AddPoints()
func TestPerpetualTradeDuplicatePointsFix(t *testing.T) {
	// Setup logger
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	// Setup
	ctx := context.Background()
	userID := uuid.New()

	// Create mock service
	mockService := &MockActivityCashbackService{}

	// Create TaskManager (the entry point for trade processing)
	taskManager := NewTaskManager(mockService)

	// Test data: $300 PERPETUAL trade should award exactly 3 points
	tradeData := map[string]interface{}{
		"trade_type": "PERPETUAL",
		"volume":     300.0,
		"cloid":      "test-cloid-123",
		"user_id":    userID.String(),
	}

	// Expected: Only TradingPointsHandler should award points
	// TradingPerpetualTradeHandler should complete task WITHOUT awarding points

	// Mock expectations for daily and trading categories
	mockService.On("UpdateActivity", ctx, userID).Return(nil).Maybe()

	// Daily category: Contains TaskIDPerpetualTradeDaily (should complete task without points)
	mockService.On("GetTasksByCategory", ctx, model.TaskCategoryName("daily")).Return([]model.ActivityTask{
		{
			ID:             uuid.New(),
			Name:           "Complete one perpetual trade",
			TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDPerpetualTradeDaily}[0],
			Points:         200, // Task points (should NOT be awarded due to our fix)
		},
	}, nil).Maybe()

	// Trading category: Contains TaskIDTradingPoints (should award volume-based points)
	mockService.On("GetTasksByCategory", ctx, model.TaskCategoryName("trading")).Return([]model.ActivityTask{
		{
			ID:             uuid.New(),
			Name:           "Trading Points",
			TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDTradingPoints}[0],
			Points:         0, // Dynamic points based on volume
		},
	}, nil).Maybe()

	mockService.On("GetTaskProgress", ctx, userID, mock.AnythingOfType("uuid.UUID")).Return(&model.UserTaskProgress{
		LastCompletedAt: nil, // Not completed today
	}, nil).Maybe()

	// CRITICAL: TradingPerpetualTradeHandler should call CompleteTask (NOT CompleteTaskWithPoints)
	mockService.On("CompleteTask", ctx, userID, mock.AnythingOfType("uuid.UUID"), mock.MatchedBy(func(data map[string]interface{}) bool {
		return data["trade_type"] == "PERPETUAL" && data["note"] == "points_awarded_by_trading_points_handler"
	})).Return(nil).Once()

	// CRITICAL: TradingPointsHandler should be the ONLY one to award points
	mockService.On("AddPoints", ctx, userID, 3, "trading_volume_300.00").Return(nil).Once()
	mockService.On("IncrementProgressWithPoints", ctx, userID, mock.AnythingOfType("uuid.UUID"), 3, 3).Return(nil).Once()

	// Act: Process the PERPETUAL trade
	err := taskManager.ProcessTradingEvent(ctx, userID, tradeData)

	// Assert: No errors and all expectations met
	assert.NoError(t, err, "ProcessTradingEvent should complete without error")
	mockService.AssertExpectations(t)

	// Verify that CompleteTaskWithPoints was NOT called (would indicate duplicate points)
	mockService.AssertNotCalled(t, "CompleteTaskWithPoints", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
}

// TestPerpetualTradePointsCalculationAfterFix verifies the correct point calculation
func TestPerpetualTradePointsCalculationAfterFix(t *testing.T) {
	testCases := []struct {
		name           string
		volume         float64
		expectedPoints int
		description    string
	}{
		{
			name:           "User_Example_300_USD",
			volume:         300.0,
			expectedPoints: 3, // 300/95 = 3.157... → 3 points
			description:    "User's exact example: $300 should award exactly 3 points",
		},
		{
			name:           "Small_Volume_95_USD",
			volume:         95.0,
			expectedPoints: 1, // 95/95 = 1.0 → 1 point
			description:    "Exactly $95 should award 1 point",
		},
		{
			name:           "Large_Volume_950_USD",
			volume:         950.0,
			expectedPoints: 10, // 950/95 = 10.0 → 10 points
			description:    "$950 should award 10 points",
		},
		{
			name:           "Very_Small_Volume_50_USD",
			volume:         50.0,
			expectedPoints: 0, // 50/95 = 0.526... → 0 points
			description:    "Small volume should award 0 points when result < 1",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup logger
			logger, _ := zap.NewDevelopment()
			global.GVA_LOG = logger

			// Setup
			ctx := context.Background()
			userID := uuid.New()
			mockService := &MockActivityCashbackService{}
			taskManager := NewTaskManager(mockService)

			tradeData := map[string]interface{}{
				"trade_type": "PERPETUAL",
				"volume":     tc.volume,
				"cloid":      "test-cloid-" + tc.name,
				"user_id":    userID.String(),
			}

			// Mock setup
			mockService.On("UpdateActivity", ctx, userID).Return(nil).Maybe()

			// Daily category: Contains TaskIDPerpetualTradeDaily
			mockService.On("GetTasksByCategory", ctx, model.TaskCategoryName("daily")).Return([]model.ActivityTask{
				{
					ID:             uuid.New(),
					Name:           "Complete one perpetual trade",
					TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDPerpetualTradeDaily}[0],
					Points:         200,
				},
			}, nil).Maybe()

			// Trading category: Contains TaskIDTradingPoints
			mockService.On("GetTasksByCategory", ctx, model.TaskCategoryName("trading")).Return([]model.ActivityTask{
				{
					ID:             uuid.New(),
					Name:           "Trading Points",
					TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDTradingPoints}[0],
					Points:         0,
				},
			}, nil).Maybe()

			mockService.On("GetTaskProgress", ctx, userID, mock.AnythingOfType("uuid.UUID")).Return(&model.UserTaskProgress{
				LastCompletedAt: nil,
			}, nil).Maybe()

			// Task completion without points
			mockService.On("CompleteTask", ctx, userID, mock.AnythingOfType("uuid.UUID"), mock.Anything).Return(nil).Maybe()

			// Points should only be awarded by TradingPointsHandler
			if tc.expectedPoints > 0 {
				expectedReason := "trading_volume_" + fmt.Sprintf("%.2f", tc.volume)
				mockService.On("AddPoints", ctx, userID, tc.expectedPoints, expectedReason).Return(nil).Once()
				mockService.On("IncrementProgressWithPoints", ctx, userID, mock.AnythingOfType("uuid.UUID"), tc.expectedPoints, tc.expectedPoints).Return(nil).Once()
			}

			// Act
			err := taskManager.ProcessTradingEvent(ctx, userID, tradeData)

			// Assert
			assert.NoError(t, err, tc.description)
			mockService.AssertExpectations(t)

			// Verify no duplicate point awarding
			mockService.AssertNotCalled(t, "CompleteTaskWithPoints", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
		})
	}
}
