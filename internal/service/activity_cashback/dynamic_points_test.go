package activity_cashback

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestCalculateDynamicMilestonePoints tests the dynamic points calculation for consecutive check-in tasks
func TestCalculateDynamicMilestonePoints(t *testing.T) {
	service := &ActivityCashbackService{}

	// Test milestones matching the user's example
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 1, Points: 50, Name: &model.MultilingualName{En: "Daily 1"}},
		{Days: 2, Points: 200, Name: &model.MultilingualName{En: "Daily 2"}},
		{Days: 4, Points: 200, Name: &model.MultilingualName{En: "Daily 4"}},
	}

	testCases := []struct {
		name           string
		currentStreak  int
		expectedPoints int
		description    string
	}{
		{
			name:           "No_Streak_Should_Show_First_Milestone_Points",
			currentStreak:  0,
			expectedPoints: 50,
			description:    "User with no streak should see points for 1-day milestone (50 points)",
		},
		{
			name:           "One_Day_Streak_Should_Show_Next_Milestone_Points",
			currentStreak:  1,
			expectedPoints: 200,
			description:    "User with 1-day streak should see points for 2-day milestone (200 points)",
		},
		{
			name:           "Two_Day_Streak_Should_Show_Next_Milestone_Points",
			currentStreak:  2,
			expectedPoints: 200,
			description:    "User with 2-day streak should see points for 4-day milestone (200 points)",
		},
		{
			name:           "Three_Day_Streak_Should_Show_Next_Milestone_Points",
			currentStreak:  3,
			expectedPoints: 200,
			description:    "User with 3-day streak should see points for 4-day milestone (200 points)",
		},
		{
			name:           "All_Milestones_Completed_Should_Show_Highest_Points",
			currentStreak:  4,
			expectedPoints: 200,
			description:    "User with 4+ day streak (all milestones completed) should see highest milestone points (200 points)",
		},
		{
			name:           "Beyond_All_Milestones_Should_Show_Highest_Points",
			currentStreak:  10,
			expectedPoints: 200,
			description:    "User with streak beyond all milestones should see highest milestone points (200 points)",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actualPoints := service.calculateDynamicMilestonePoints(milestones, tc.currentStreak)
			assert.Equal(t, tc.expectedPoints, actualPoints, 
				"Expected %d points for streak %d, got %d. %s", 
				tc.expectedPoints, tc.currentStreak, actualPoints, tc.description)
		})
	}
}

// TestCalculateDynamicMilestonePointsVariousConfigurations tests different milestone configurations
func TestCalculateDynamicMilestonePointsVariousConfigurations(t *testing.T) {
	service := &ActivityCashbackService{}

	t.Run("Traditional_3_7_30_Configuration", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 50},
			{Days: 7, Points: 200},
			{Days: 30, Points: 1000},
		}

		testCases := []struct {
			streak         int
			expectedPoints int
		}{
			{0, 50},   // Should target 3-day milestone
			{1, 50},   // Should target 3-day milestone
			{2, 50},   // Should target 3-day milestone
			{3, 200},  // Should target 7-day milestone
			{4, 200},  // Should target 7-day milestone
			{7, 1000}, // Should target 30-day milestone
			{15, 1000}, // Should target 30-day milestone
			{30, 1000}, // All completed, show highest
			{50, 1000}, // All completed, show highest
		}

		for _, tc := range testCases {
			actualPoints := service.calculateDynamicMilestonePoints(milestones, tc.streak)
			assert.Equal(t, tc.expectedPoints, actualPoints,
				"For streak %d, expected %d points, got %d", tc.streak, tc.expectedPoints, actualPoints)
		}
	})

	t.Run("Single_Milestone_Configuration", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 5, Points: 100},
		}

		testCases := []struct {
			streak         int
			expectedPoints int
		}{
			{0, 100}, // Should target the only milestone
			{1, 100}, // Should target the only milestone
			{4, 100}, // Should target the only milestone
			{5, 100}, // Completed, show the milestone points
			{10, 100}, // Completed, show the milestone points
		}

		for _, tc := range testCases {
			actualPoints := service.calculateDynamicMilestonePoints(milestones, tc.streak)
			assert.Equal(t, tc.expectedPoints, actualPoints,
				"For single milestone config with streak %d, expected %d points, got %d", 
				tc.streak, tc.expectedPoints, actualPoints)
		}
	})

	t.Run("Empty_Milestones_Should_Return_Zero", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{}
		actualPoints := service.calculateDynamicMilestonePoints(milestones, 5)
		assert.Equal(t, 0, actualPoints, "Empty milestones should return 0 points")
	})
}

// TestGetHighestMilestone tests the helper method for finding the highest milestone
func TestGetHighestMilestone(t *testing.T) {
	service := &ActivityCashbackService{}

	t.Run("Multiple_Milestones_Should_Return_Highest", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 4, Points: 200},
			{Days: 2, Points: 100},
		}

		highest := service.getHighestMilestone(milestones)
		assert.NotNil(t, highest, "Should return a milestone")
		assert.Equal(t, 4, highest.Days, "Should return milestone with 4 days")
		assert.Equal(t, 200, highest.Points, "Should return milestone with 200 points")
	})

	t.Run("Single_Milestone_Should_Return_That_Milestone", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 7, Points: 500},
		}

		highest := service.getHighestMilestone(milestones)
		assert.NotNil(t, highest, "Should return a milestone")
		assert.Equal(t, 7, highest.Days, "Should return the only milestone")
		assert.Equal(t, 500, highest.Points, "Should return the only milestone points")
	})

	t.Run("Empty_Milestones_Should_Return_Nil", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{}
		highest := service.getHighestMilestone(milestones)
		assert.Nil(t, highest, "Empty milestones should return nil")
	})
}

// TestDynamicPointsIntegrationScenarios tests real-world scenarios
func TestDynamicPointsIntegrationScenarios(t *testing.T) {
	service := &ActivityCashbackService{}

	t.Run("User_Example_Scenario", func(t *testing.T) {
		// This matches the user's exact example from the request
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 200},
			{Days: 4, Points: 200},
		}

		// User has streakCount: 1, should see points for 2-day milestone (200 points)
		actualPoints := service.calculateDynamicMilestonePoints(milestones, 1)
		assert.Equal(t, 200, actualPoints, 
			"User with 1-day streak should see 200 points (2-day milestone)")
	})

	t.Run("Progressive_Milestone_Display", func(t *testing.T) {
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 10},
			{Days: 3, Points: 50},
			{Days: 7, Points: 150},
			{Days: 14, Points: 400},
			{Days: 30, Points: 1000},
		}

		// Test progression through milestones
		progressionTests := []struct {
			day            int
			expectedPoints int
			description    string
		}{
			{0, 10, "Day 0: Should show 1-day milestone (10 points)"},
			{1, 50, "Day 1: Should show 3-day milestone (50 points)"},
			{2, 50, "Day 2: Should show 3-day milestone (50 points)"},
			{3, 150, "Day 3: Should show 7-day milestone (150 points)"},
			{5, 150, "Day 5: Should show 7-day milestone (150 points)"},
			{7, 400, "Day 7: Should show 14-day milestone (400 points)"},
			{10, 400, "Day 10: Should show 14-day milestone (400 points)"},
			{14, 1000, "Day 14: Should show 30-day milestone (1000 points)"},
			{20, 1000, "Day 20: Should show 30-day milestone (1000 points)"},
			{30, 1000, "Day 30: All completed, show highest (1000 points)"},
			{50, 1000, "Day 50: All completed, show highest (1000 points)"},
		}

		for _, test := range progressionTests {
			actualPoints := service.calculateDynamicMilestonePoints(milestones, test.day)
			assert.Equal(t, test.expectedPoints, actualPoints, test.description)
		}
	})
}
