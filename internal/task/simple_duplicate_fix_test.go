package task

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestHyperLiquidDuplicateFixLogic tests the core logic of the duplicate fix
func TestHyperLiquidDuplicateFixLogic(t *testing.T) {
	t.Run("NewTransaction_ShouldProcessTask", func(t *testing.T) {
		// Simulate new transaction scenario
		existingTx := (*interface{})(nil) // No existing transaction
		isNewTransaction := existingTx == nil

		// Should process task for new transactions
		shouldProcessTask := isNewTransaction
		assert.True(t, shouldProcessTask, "New transactions should process tasks")
	})

	t.Run("ExistingTransaction_ShouldSkipTask", func(t *testing.T) {
		// Simulate existing transaction scenario
		existingTx := &struct{}{} // Existing transaction found
		isNewTransaction := existingTx == nil

		// Should NOT process task for existing transactions
		shouldProcessTask := isNewTransaction
		assert.False(t, shouldProcessTask, "Existing transactions should NOT process tasks")
	})
}

// TestVolumeCalculation tests the volume calculation logic
func TestVolumeCalculation(t *testing.T) {
	testCases := []struct {
		name     string
		avgPrice float64
		size     float64
		expected float64
	}{
		{
			name:     "User_Example_300_USD",
			avgPrice: 117104.0,
			size:     0.00256,
			expected: 299.79, // 117104 * 0.00256 ≈ 299.79
		},
		{
			name:     "Small_Volume",
			avgPrice: 50000.0,
			size:     0.001,
			expected: 50.0,
		},
		{
			name:     "Large_Volume",
			avgPrice: 100000.0,
			size:     0.01,
			expected: 1000.0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			volume := tc.avgPrice * tc.size
			assert.InDelta(t, tc.expected, volume, 0.01, "Volume calculation should be correct")

			// Test points calculation (volume/95)
			expectedPoints := int(volume / 95.0)
			actualPoints := int(volume / 95.0)
			assert.Equal(t, expectedPoints, actualPoints, "Points calculation should be correct")
		})
	}
}

// TestPointsCalculation tests the PERPETUAL points calculation
func TestPointsCalculation(t *testing.T) {
	testCases := []struct {
		name           string
		volume         float64
		expectedPoints int
	}{
		{
			name:           "User_Example_300_USD",
			volume:         299.79,
			expectedPoints: 3, // 299.79/95 ≈ 3.155 → 3
		},
		{
			name:           "Small_Volume_95_USD",
			volume:         95.0,
			expectedPoints: 1, // 95/95 = 1
		},
		{
			name:           "Large_Volume_950_USD",
			volume:         950.0,
			expectedPoints: 10, // 950/95 = 10
		},
		{
			name:           "Very_Small_Volume_50_USD",
			volume:         50.0,
			expectedPoints: 0, // 50/95 ≈ 0.526 → 0
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// PERPETUAL trades: direct point calculation (volume/95)
			directPoints := tc.volume / 95.0
			points := int(directPoints)
			if points < 0 {
				points = 0
			}

			assert.Equal(t, tc.expectedPoints, points, "PERPETUAL points calculation should be correct")
		})
	}
}
