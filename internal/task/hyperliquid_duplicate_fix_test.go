package task

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// MockHyperLiquidTransactionService for testing
type MockHyperLiquidTransactionService struct {
	mock.Mock
}

func (m *MockHyperLiquidTransactionService) FindHyperLiquidTransactionByCloid(ctx context.Context, cloid string) (*model.HyperLiquidTransaction, error) {
	args := m.Called(ctx, cloid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.HyperLiquidTransaction), args.Error(1)
}

func (m *MockHyperLiquidTransactionService) UpdateHyperLiquidTransactionByCloid(ctx context.Context, cloid string, tx *model.HyperLiquidTransaction) error {
	args := m.Called(ctx, cloid, tx)
	return args.Error(0)
}

func (m *MockHyperLiquidTransactionService) BulkInsertHyperLiquidTransactionsEvent(ctx context.Context, txs []model.HyperLiquidTransaction) error {
	args := m.Called(ctx, txs)
	return args.Error(0)
}

// MockContractCommissionService for testing
type MockContractCommissionService struct {
	mock.Mock
}

func (m *MockContractCommissionService) ProcessContractCommission(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

// TestHyperLiquidDuplicateTaskProcessingFix tests that task processing only happens for NEW transactions
func TestHyperLiquidDuplicateTaskProcessingFix(t *testing.T) {
	// Setup logger
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	ctx := context.Background()
	userID := uuid.New()
	cloid := "test-cloid-300-fix"

	// Create test transaction
	status := "filled"
	avgPrice := decimal.NewFromFloat(117104.0)
	size := decimal.NewFromFloat(0.00256)
	buildFee := decimal.NewFromFloat(0.1)
	testTx := &model.HyperLiquidTransaction{
		Cloid:    cloid,
		UserID:   &userID,
		Status:   &status,
		AvgPrice: &avgPrice,
		Size:     &size,
		BuildFee: &buildFee,
	}

	t.Run("NewTransaction_ShouldProcessTask", func(t *testing.T) {
		// Setup mocks
		mockTxService := &MockHyperLiquidTransactionService{}
		mockCommissionService := &MockContractCommissionService{}
		mockActivityService := &activity_cashback.MockActivityCashbackService{}

		// Mock: Transaction doesn't exist (new transaction)
		mockTxService.On("FindHyperLiquidTransactionByCloid", ctx, cloid).Return(nil, nil)
		mockTxService.On("BulkInsertHyperLiquidTransactionsEvent", ctx, mock.AnythingOfType("[]model.HyperLiquidTransaction")).Return(nil)
		mockCommissionService.On("ProcessContractCommission", ctx, mock.AnythingOfType("*model.HyperLiquidTransaction")).Return(nil)

		// Mock activity service for task processing
		mockActivityService.On("UpdateActivity", ctx, userID).Return(nil).Maybe()
		mockActivityService.On("GetTasksByCategory", ctx, model.TaskCategoryName("daily")).Return([]model.ActivityTask{
			{
				ID:             uuid.New(),
				TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDPerpetualTradeDaily}[0],
				Points:         200,
			},
		}, nil).Maybe()
		mockActivityService.On("GetTasksByCategory", ctx, model.TaskCategoryName("trading")).Return([]model.ActivityTask{
			{
				ID:             uuid.New(),
				TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDTradingPoints}[0],
				Points:         0,
			},
		}, nil).Maybe()
		mockActivityService.On("GetTaskProgress", ctx, userID, mock.AnythingOfType("uuid.UUID")).Return(&model.UserTaskProgress{
			LastCompletedAt: nil,
		}, nil).Maybe()
		mockActivityService.On("CompleteTask", ctx, userID, mock.AnythingOfType("uuid.UUID"), mock.Anything).Return(nil).Maybe()
		mockActivityService.On("AddPoints", ctx, userID, 3, "trading_volume_299.79").Return(nil).Once()
		mockActivityService.On("IncrementProgressWithPoints", ctx, userID, mock.AnythingOfType("uuid.UUID"), 3, 3).Return(nil).Once()

		// Create TaskManager
		taskManager := activity_cashback.NewTaskManager(mockActivityService)

		// Test the processing logic directly
		err := processDerivativesTradeTaskCompletion(ctx, testTx, taskManager)
		assert.NoError(t, err)

		// Verify that points were awarded (task was processed)
		mockActivityService.AssertExpectations(t)
		mockActivityService.AssertCalled(t, "AddPoints", ctx, userID, 3, "trading_volume_299.79")
	})

	t.Run("ExistingTransaction_ShouldSkipTaskProcessing", func(t *testing.T) {
		// Setup mocks
		mockTxService := &MockHyperLiquidTransactionService{}
		mockCommissionService := &MockContractCommissionService{}
		mockActivityService := &activity_cashback.MockActivityCashbackService{}

		// Mock: Transaction already exists (update scenario)
		existingTx := &model.HyperLiquidTransaction{
			Cloid:  cloid,
			UserID: &userID,
		}
		mockTxService.On("FindHyperLiquidTransactionByCloid", ctx, cloid).Return(existingTx, nil)
		mockTxService.On("UpdateHyperLiquidTransactionByCloid", ctx, cloid, mock.AnythingOfType("*model.HyperLiquidTransaction")).Return(nil)
		mockCommissionService.On("ProcessContractCommission", ctx, mock.AnythingOfType("*model.HyperLiquidTransaction")).Return(nil)

		// Activity service should NOT be called for task processing
		// (no expectations set means any call will fail the test)

		// Create event
		status := "filled"
		avgPrice := decimal.NewFromFloat(117104.0)
		size := decimal.NewFromFloat(0.00256)
		buildFee := decimal.NewFromFloat(0.1)
		event := HyperLiquidTransactionEvent{
			Cloid:    cloid,
			UserID:   userID,
			Status:   &status,
			AvgPrice: &avgPrice,
			Size:     &size,
			BuildFee: &buildFee,
		}

		// Process the event (simulating the actual NATS processing logic)
		transactionEvent := model.HyperLiquidTransaction{
			Cloid:    event.Cloid,
			UserID:   &event.UserID,
			Status:   event.Status,
			AvgPrice: event.AvgPrice,
			Size:     event.Size,
			BuildFee: event.BuildFee,
		}

		now := time.Now()
		transactionEvent.CreatedTime = &now

		// Check if transaction exists
		existingTx, err := mockTxService.FindHyperLiquidTransactionByCloid(ctx, event.Cloid)
		assert.NoError(t, err)
		assert.NotNil(t, existingTx)

		// Update existing transaction
		err = mockTxService.UpdateHyperLiquidTransactionByCloid(ctx, event.Cloid, &transactionEvent)
		assert.NoError(t, err)

		// Process commission
		err = mockCommissionService.ProcessContractCommission(ctx, &transactionEvent)
		assert.NoError(t, err)

		// Task processing should be SKIPPED for existing transactions
		// (this is the key fix - no task processing for updates)

		// Verify mocks
		mockTxService.AssertExpectations(t)
		mockCommissionService.AssertExpectations(t)

		// Verify that NO activity service methods were called
		mockActivityService.AssertNotCalled(t, "AddPoints", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
		mockActivityService.AssertNotCalled(t, "IncrementProgressWithPoints", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})
}

// TestProcessDerivativesTradeTaskCompletion tests the task completion logic
func TestProcessDerivativesTradeTaskCompletion(t *testing.T) {
	// Setup logger
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	ctx := context.Background()
	userID := uuid.New()

	status := "filled"
	avgPrice := decimal.NewFromFloat(117104.0)
	size := decimal.NewFromFloat(0.00256)
	testTx := &model.HyperLiquidTransaction{
		Cloid:    "test-300-volume",
		UserID:   &userID,
		Status:   &status,
		AvgPrice: &avgPrice,
		Size:     &size, // Volume = 117104 * 0.00256 ≈ 299.79
	}

	mockActivityService := &activity_cashback.MockActivityCashbackService{}

	// Setup mocks for successful task processing
	mockActivityService.On("UpdateActivity", ctx, userID).Return(nil).Maybe()
	mockActivityService.On("GetTasksByCategory", ctx, model.TaskCategoryName("daily")).Return([]model.ActivityTask{
		{
			ID:             uuid.New(),
			TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDPerpetualTradeDaily}[0],
			Points:         200,
		},
	}, nil).Maybe()
	mockActivityService.On("GetTasksByCategory", ctx, model.TaskCategoryName("trading")).Return([]model.ActivityTask{
		{
			ID:             uuid.New(),
			TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDTradingPoints}[0],
			Points:         0,
		},
	}, nil).Maybe()
	mockActivityService.On("GetTaskProgress", ctx, userID, mock.AnythingOfType("uuid.UUID")).Return(&model.UserTaskProgress{
		LastCompletedAt: nil,
	}, nil).Maybe()
	mockActivityService.On("CompleteTask", ctx, userID, mock.AnythingOfType("uuid.UUID"), mock.Anything).Return(nil).Maybe()
	mockActivityService.On("AddPoints", ctx, userID, 3, "trading_volume_299.79").Return(nil).Once()
	mockActivityService.On("IncrementProgressWithPoints", ctx, userID, mock.AnythingOfType("uuid.UUID"), 3, 3).Return(nil).Once()

	taskManager := activity_cashback.NewTaskManager(mockActivityService)

	// Test task processing
	err := processDerivativesTradeTaskCompletion(ctx, testTx, taskManager)
	assert.NoError(t, err)

	// Verify expectations
	mockActivityService.AssertExpectations(t)
	mockActivityService.AssertCalled(t, "AddPoints", ctx, userID, 3, "trading_volume_299.79")
}
