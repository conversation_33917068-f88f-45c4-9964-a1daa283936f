package main

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	// Initialize the application
	global.GVA_VP = initializer.Viper("config.yaml")
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	fmt.Println("🔍 DEBUG: Checking for duplicate TRADING_POINTS tasks...")

	ctx := context.Background()

	// Create service
	service := activity_cashback.NewActivityCashbackService()

	// Get all tasks in trading category
	tradingTasks, err := service.GetTasksByCategory(ctx, "trading")
	if err != nil {
		log.Fatalf("Failed to get trading tasks: %v", err)
	}

	fmt.Printf("📊 Found %d tasks in trading category:\n", len(tradingTasks))

	tradingPointsTasks := []model.ActivityTask{}
	perpetualTasks := []model.ActivityTask{}

	for _, task := range tradingTasks {
		fmt.Printf("  - ID: %s, Name: %s, Identifier: %v, Points: %d\n", 
			task.ID.String(), task.Name, task.TaskIdentifier, task.Points)

		if task.TaskIdentifier != nil {
			switch *task.TaskIdentifier {
			case model.TaskIDTradingPoints:
				tradingPointsTasks = append(tradingPointsTasks, task)
			case model.TaskIDPerpetualTradeDaily:
				perpetualTasks = append(perpetualTasks, task)
			}
		}
	}

	fmt.Printf("\n🎯 TRADING_POINTS tasks found: %d\n", len(tradingPointsTasks))
	for i, task := range tradingPointsTasks {
		fmt.Printf("  %d. ID: %s, Name: %s, Points: %d, Active: %t\n", 
			i+1, task.ID.String(), task.Name, task.Points, task.IsActive)
	}

	fmt.Printf("\n🎯 PERPETUAL_TRADE_DAILY tasks in TRADING category: %d\n", len(perpetualTasks))
	for i, task := range perpetualTasks {
		fmt.Printf("  %d. ID: %s, Name: %s, Points: %d, Active: %t\n", 
			i+1, task.ID.String(), task.Name, task.Points, task.IsActive)
	}

	// Also check daily category
	dailyTasks, err := service.GetTasksByCategory(ctx, "daily")
	if err != nil {
		log.Fatalf("Failed to get daily tasks: %v", err)
	}

	fmt.Printf("\n📊 Found %d tasks in daily category:\n", len(dailyTasks))

	dailyPerpetualTasks := []model.ActivityTask{}
	for _, task := range dailyTasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDPerpetualTradeDaily {
			dailyPerpetualTasks = append(dailyPerpetualTasks, task)
			fmt.Printf("  - PERPETUAL: ID: %s, Name: %s, Points: %d, Active: %t\n", 
				task.ID.String(), task.Name, task.Points, task.IsActive)
		}
	}

	fmt.Printf("\n🎯 PERPETUAL_TRADE_DAILY tasks in DAILY category: %d\n", len(dailyPerpetualTasks))

	// Test the actual processing
	fmt.Println("\n🧪 Testing PERPETUAL trade processing...")
	
	testUserID := uuid.New()
	tradeData := map[string]interface{}{
		"trade_type": "PERPETUAL",
		"volume":     300.0,
		"cloid":      "debug-test-300",
		"user_id":    testUserID.String(),
	}

	taskManager := activity_cashback.NewTaskManager(service)
	
	fmt.Printf("Processing trade for user: %s\n", testUserID.String())
	if err := taskManager.ProcessTradingEvent(ctx, testUserID, tradeData); err != nil {
		log.Printf("Error processing trade: %v", err)
	} else {
		fmt.Println("✅ Trade processed successfully")
	}

	fmt.Println("\n🔍 Summary:")
	fmt.Printf("- TRADING_POINTS tasks in trading category: %d\n", len(tradingPointsTasks))
	fmt.Printf("- PERPETUAL_TRADE_DAILY tasks in trading category: %d\n", len(perpetualTasks))
	fmt.Printf("- PERPETUAL_TRADE_DAILY tasks in daily category: %d\n", len(dailyPerpetualTasks))

	if len(tradingPointsTasks) > 1 {
		fmt.Println("🚨 PROBLEM: Multiple TRADING_POINTS tasks found!")
	}
	if len(perpetualTasks) > 0 {
		fmt.Println("🚨 PROBLEM: PERPETUAL_TRADE_DAILY tasks found in trading category!")
	}
}
